# Docker Deployment Guide

This guide explains how to deploy the Rakamin Profile Viz application using Docker with nginx as a reverse proxy.

## Architecture

The Docker setup includes:
- **Frontend**: React app built with Vite, served as static files by nginx
- **Backend**: Node.js/Express API server running on port 3001
- **Nginx**: Reverse proxy that routes:
  - `/` → Frontend static files
  - `/api/*` → Backend API server (strips `/api` prefix)

## Quick Start

### 1. Environment Setup

Copy the environment template and update with your values:

```bash
cp .env.production .env
```

Edit `.env` and set your Gemini API key:

```env
GEMINI_API_KEY=your_actual_gemini_api_key_here
```

### 2. Build and Run with Docker Compose

```bash
# Build and start the application
docker-compose up --build

# Or run in detached mode
docker-compose up --build -d
```

The application will be available at `http://localhost`

### 3. Manual Docker Build

If you prefer to build manually:

```bash
# Build the image
docker build -t rakamin-profile-viz .

# Run the container
docker run -p 80:80 \
  -e GEMINI_API_KEY=your_api_key_here \
  -e LOGIN_EMAIL=<EMAIL> \
  -e LOGIN_PASSWORD=Rakamin2025 \
  rakamin-profile-viz
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Backend server port | `3001` |
| `LOGIN_EMAIL` | Admin login email | `<EMAIL>` |
| `LOGIN_PASSWORD` | Admin login password | `Rakamin2025` |
| `GEMINI_API_KEY` | Google Gemini API key | **Required** |
| `MODEL_ID` | Gemini model to use | `gemini-2.5-pro` |

## API Endpoints

All API endpoints are accessible via the `/api` prefix:

- `POST /api/login` - User authentication
- `POST /api/company_profiling` - Start background company profiling job
- `GET /api/job/:jobId` - Check job status and get results
- `GET /api/health` - Health check

## Development vs Production

### Development Mode
- Frontend: `http://localhost:8080` (Vite dev server)
- Backend: `http://localhost:3001` (Direct API access)
- API calls use `http://localhost:3001/api`

### Production Mode (Docker)
- Application: `http://localhost` (nginx proxy)
- API calls use `/api` (relative URLs)
- nginx handles routing and static file serving

## Troubleshooting

### Container won't start
```bash
# Check logs
docker-compose logs

# Check if ports are available
netstat -tulpn | grep :80
```

### API connection issues
```bash
# Test health endpoint
curl http://localhost/api/health

# Check backend logs
docker-compose logs rakamin-profile-viz
```

### Frontend not loading
```bash
# Verify nginx is serving static files
curl http://localhost

# Check nginx configuration
docker-compose exec rakamin-profile-viz nginx -t
```

## Stopping the Application

```bash
# Stop containers
docker-compose down

# Stop and remove volumes
docker-compose down -v
```
