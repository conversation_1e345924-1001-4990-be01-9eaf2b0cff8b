version: '3.8'

services:
  rakamin-profile-viz:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      # Backend environment variables
      - PORT=3001
      - LOGIN_EMAIL=${LOGIN_EMAIL:-<EMAIL>}
      - LOGIN_PASSWORD=${LOGIN_PASSWORD:-Rakamin2025}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - MODEL_ID=${MODEL_ID:-gemini-2.5-pro}
      # Frontend environment variables (for build time)
      - VITE_API_URL=/api
    env_file:
      - .env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
