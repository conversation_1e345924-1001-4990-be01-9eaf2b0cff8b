/* eslint-disable @typescript-eslint/no-explicit-any */
import express, { Request, Response } from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// =================================================================
// JOB MANAGEMENT TYPES AND STORAGE
// =================================================================

interface JobStatus {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  companyName: string;
  result?: object;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

// In-memory job storage (in production, consider Redis or similar)
const jobs = new Map<string, JobStatus>();

// Clean up old jobs (older than 1 hour)
const cleanupOldJobs = () => {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  for (const [jobId, job] of jobs.entries()) {
    if (job.createdAt < oneHourAgo) {
      jobs.delete(jobId);
    }
  }
};

// Run cleanup every 30 minutes
setInterval(cleanupOldJobs, 30 * 60 * 1000);

// =================================================================
// CONFIGURATION
// =================================================================

/**
 * Loads, validates, and returns application configuration from environment variables.
 * Exits the process if a required variable is missing.
 */
const getAppConfig = () => {
  dotenv.config();

  const requiredEnvVars = ['LOGIN_EMAIL', 'LOGIN_PASSWORD', 'GEMINI_API_KEY', 'TAVILY_API_KEY'];

  for (const varName of requiredEnvVars) {
    if (!process.env[varName]) {
      console.error(`FATAL: Missing required environment variable: ${varName}`);
      process.exit(1); // Fail fast if configuration is incomplete
    }
  }

  return {
    port: process.env.PORT || 3001,
    login: {
      email: process.env.LOGIN_EMAIL!,
      password: process.env.LOGIN_PASSWORD!,
    },
    gemini: {
      apiKey: process.env.GEMINI_API_KEY!,
      modelId: process.env.MODEL_ID || 'gemini-2.5-flash', // Using a standard, recent model
    },
    tavily: {
      apiKey: process.env.TAVILY_API_KEY!,
    },
  };
};

const CONFIG = getAppConfig();

// =================================================================
// REACT PARADIGM TYPES AND INTERFACES
// =================================================================

interface SearchResult {
  title: string;
  url: string;
  content: string;
  score: number;
}

interface TavilyResponse {
  query: string;
  results: SearchResult[];
}

interface ReActIteration {
  reasoning: string;
  action: string;
  query: string;
  observation: string;
  information_gathered: any;
  dataType?: string;
}

interface CompanyInformation {
  basic_info: any;
  products_and_services: string;
  management_profile: any[];
  group_structure: any;
  esg_initiatives: any;
  growth_metrics: any;
  industry_insights: any[];
  recent_news: any[];
  recent_socmed_posts: any[];
}

interface ExtractionResult extends Partial<CompanyInformation> {
  has_new_information?: boolean;
}

// =================================================================
// HELPER FUNCTIONS
// =================================================================

/**
 * Reads a prompt file from the './server' directory relative to the project root.
 * @param fileName The name of the file to read (e.g., 'system_prompt.md').
 * @returns The content of the file as a string.
 */
const readPromptFile = (fileName: string): string => {
  try {
    if (process.env.NODE_ENV === 'production') {
      const filePath = path.join(process.cwd(), 'prompts', fileName);
      return fs.readFileSync(filePath, 'utf8');
    }

    const filePath = path.join(process.cwd(), 'server', 'prompts', fileName);
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Error reading prompt file at ${path.join('server', fileName)}:`, error);
    throw new Error(`Could not read instruction file: ${fileName}`);
  }
};

/**
 * Calls the Tavily API to perform web searches.
 * @param query The search query string.
 * @returns A promise that resolves to the search results.
 */
const callTavilyAPI = async (query: string): Promise<TavilyResponse> => {
  const { apiKey } = CONFIG.tavily;
  const url = 'https://api.tavily.com/search';

  const requestBody = {
    query: query,
    search_depth: "advanced",
    include_answer: false,
    include_raw_content: false,
    max_results: 10,
    include_domains: [],
    exclude_domains: []
  };

  try {
    console.log(`Calling Tavily API with query: "${query}"`);
    const response = await axios.post(url, requestBody, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      }
    });

    if (!response.data || !response.data.results) {
      throw new Error('Invalid response from Tavily API');
    }

    console.log(`Tavily API returned ${response.data.results.length} results`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Axios error calling Tavily API:', error.response?.data || error.message);
    } else {
      console.error('Unexpected error in callTavilyAPI:', error);
    }
    throw error;
  }
};

/**
 * Extracts a JSON object from a string, which may be wrapped in markdown code blocks.
 * @param text The string potentially containing the JSON.
 * @returns The parsed JavaScript object.
 */
const extractAndParseJson = (text: string): object => {
  // Regex to find JSON in a markdown block ```json ... ``` or as a raw object { ... }.
  const jsonMatch = text.match(/```json\s*([\s\S]*?)\s*```|(\{[\s\S]*\})/);

  if (!jsonMatch) {
    console.error("Raw text from AI that failed parsing:", text);
    throw new Error('No valid JSON object found in the response from the AI model.');
  }

  // The first capturing group is for the markdown block, the second for the raw object.
  const jsonString = jsonMatch[1] || jsonMatch[2];

  try {
    return JSON.parse(jsonString);
  } catch (parseError) {
    console.error('Failed to parse the following JSON string:', jsonString);
    throw new Error('The AI model returned a malformed JSON object.');
  }
};

/**
 * Calls the Google Gemini API with a given prompt and system instruction.
 * @param systemInstruction The system-level instructions for the model.
 * @param userPrompt The user's prompt for this specific task.
 * @param searchResults Optional search results to include in the context.
 * @returns A promise that resolves to the parsed JSON object from the model's response.
 */
const callGemini = async (systemInstruction: string, userPrompt: string, searchResults?: SearchResult[]): Promise<object> => {
  const { apiKey, modelId } = CONFIG.gemini;
  const url = `https://generativelanguage.googleapis.com/v1beta/models/${modelId}:generateContent?key=${apiKey}`;

  // Include search results in the prompt if provided
  let enhancedPrompt = userPrompt;
  if (searchResults && searchResults.length > 0) {
    const searchContext = searchResults.map((result, index) =>
      `[Search Result ${index + 1}]\nTitle: ${result.title}\nURL: ${result.url}\nContent: ${result.content}\n`
    ).join('\n');

    enhancedPrompt = `${userPrompt}\n\n--- SEARCH RESULTS ---\n${searchContext}`;
  }

  const requestBody = {
    contents: [{ role: "user", parts: [{ text: enhancedPrompt }] }],
    generationConfig: {
      temperature: 0.4,
      thinkingConfig: { thinkingBudget: 24576 },
    },
    system_instruction: { parts: [{ text: systemInstruction }] },
    // Removed Google Search tools - we'll use Tavily instead
  };

  try {
    console.log(`Calling Gemini model ${modelId}...`);
    const response = await axios.post(url, requestBody, {
      headers: { 'Content-Type': 'application/json' }
    });

    const generatedContent = response.data.candidates?.[0]?.content?.parts?.[0]?.text;
    console.log("Raw Gemini Response:", generatedContent?.substring(0, 500) + "...");

    if (!generatedContent) {
      console.error("Raw Gemini Response:", JSON.stringify(response.data));
      throw new Error('No content was generated by the Gemini API.');
    }

    return extractAndParseJson(generatedContent);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Axios error calling Gemini API:', error.response?.data || error.message);
    } else {
      console.error('Unexpected error in callGemini:', error);
    }
    // Re-throw the error to be caught by the route handler
    throw error;
  }
};

// =================================================================
// REACT PARADIGM IMPLEMENTATION
// =================================================================



/**
 * Performs reasoning to determine what information is still needed.
 * @param companyName The company being researched.
 * @param currentInfo The information gathered so far.
 * @param iteration The current iteration number.
 * @param previousQueries Array of previously executed queries to avoid duplicates.
 * @param failedDataTypes Set of data types that have been searched but not found.
 * @returns The reasoning and next action to take.
 */
const performReasoning = async (
  companyName: string,
  currentInfo: Partial<CompanyInformation>,
  iteration: number,
  previousQueries: string[] = [],
  failedDataTypes: Set<string> = new Set()
): Promise<{reasoning: string, action: string, query: string, dataType: string}> => {

  // Let AI decide priorities and queries dynamically
  const reasoningPrompt = `
You are a business research analyst using the ReAct (Reasoning and Acting) paradigm to gather comprehensive company information.
Current Date: ${new Date().toISOString()}

Company: ${companyName}
Current Iteration: ${iteration}
Previous Queries: ${JSON.stringify(previousQueries)}
Failed Data Types (no data found): ${JSON.stringify(Array.from(failedDataTypes))}

Current Information Gathered:
${JSON.stringify(currentInfo, null, 2)}

CONTEXT FOR DECISION MAKING:
- Failed Data Types: These data types have been searched but yielded no meaningful results. Avoid searching for these again unless you have a significantly different search strategy.
- Previous Queries: These queries have already been executed. Avoid similar or duplicate queries.
- Current Information: Analyze what data is already available and what gaps exist.

INSTRUCTIONS:
1. Analyze the current information and identify the most critical missing data
2. Consider the business value and importance of different data types for company profiling
3. Prioritize data types that would provide the most comprehensive company understanding
4. Avoid data types in the Failed Data Types list unless you have a compelling reason and different approach
5. Create targeted, specific search queries that are different from previous queries
6. Consider the iteration number - early iterations should focus on foundational data, later iterations on specialized information
7. Prioritize queries for news first, then social media posts, then other data types

Available Data Types and their purposes:
- news: Recent company developments, announcements, press releases
- social_media: Social media presence, recent posts, engagement
- basic: Company fundamentals (name, industry, location, description, website)
- financial: Revenue, funding, business model, financial performance
- leadership: Management team, executives, founders, key personnel
- additional: Industry insights, ESG initiatives, growth metrics, group structure

Your response should be in this exact format:
REASONING: [Explain what information is missing, why it's important, and your search strategy. Consider failed data types and previous queries in your reasoning.]
ACTION: search
QUERY: [Specific, targeted search query that differs from previous queries]
DATA_TYPE: [One of: news, social_media, basic, financial, leadership, additional]

Your goal is to help build a comprehensive company profile with this structure:
{
  "company_profile": {
    "basic_info": {
      "company_name": "string",
      "logo_url": "string (if available)",
      "industry_category": "string (e.g., FMCG - snack & confectionery manufacturing)",
      "description": "string (2-3 sentences describing the business)",
      "location": "string (City, Region/State)",
      "employee_count": "string (e.g., 1,200 employees)",
      "established_year": "string (e.g., Est. 2000)",
      "website": "string (company website URL)"
    },
    "products_and_services": "string (few sentences explaining what the company offers as products/services)",
    "management_profile": {
      "name": "string",
      "position": "string (e.g., Founder & CEO, Operations Manager)",
      "background": "string (professional background description)"
    }[],
    "group_structure": {
      "parent_company": "string",
      "subsidiaries": {
        "name": "string",
        "description": "string"
      }[],
      "ownership_structure": "string (e.g., privately held, public equity details)"
    },
    "esg_initiatives": {
      "environmental": "string (environmental initiatives)"[],
      "social": "string (social responsibility programs)"[],
      "governance": "string (governance practices)"[]
    },
    "growth_metrics": {
      "cagr": {
        "percentage": "float (e.g., 0.39 for 39%)",
        "benchmark": "string (e.g., Industry Growth)",
        "data_points": {
          "year": "number",
          "value": "float (e.g., 2000000000.0, 4000000000.0)"
        }[]
      }
    },
    "industry_insights": {
      "title": "string",
      "description": "string (insights about the industry the company operates in, not about the company itself)"
    }[],
    "recent_news": {
      "title": "string",
      "date": "string (DD Month YYYY format)",
      "categories": ["string", "string"],
      "summary": "string (brief summary)",
      "source_url": "string (valid URL link to the news article)"
    }[],
    "recent_socmed_posts": {
      "title": "string",
      "date": "string (DD Month YYYY format)",
      "socmed_type": "string (enum: x, instagram, linkedin, facebook, youtube)",
      "summary": "string (brief summary)",
      "source_url": "string (valid URL link to the social media post)"
    }[]
  }
}

Make your search query targeted and specific to get the most relevant results.
`;

  try {
    const response = await axios.post(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=${CONFIG.gemini.apiKey}`, {
      contents: [{ role: "user", parts: [{ text: reasoningPrompt }] }],
      generationConfig: {
        temperature: 0.4,
        thinkingConfig: { thinkingBudget: 8192 }
      }
    }, {
      headers: { 'Content-Type': 'application/json' }
    });

    const content = response.data.candidates?.[0]?.content?.parts?.[0]?.text || '';

    // Parse the structured response
    const reasoningMatch = content.match(/REASONING:\s*(.*?)(?=ACTION:|$)/s);
    const actionMatch = content.match(/ACTION:\s*(.*?)(?=QUERY:|$)/s);
    const queryMatch = content.match(/QUERY:\s*(.*?)(?=DATA_TYPE:|$)/s);
    const dataTypeMatch = content.match(/DATA_TYPE:\s*(.*?)$/s);

    const reasoning = reasoningMatch?.[1]?.trim() || 'Continuing research to gather comprehensive company information.';
    const action = actionMatch?.[1]?.trim() || 'search';
    const query = queryMatch?.[1]?.trim() || `${companyName} company information`;
    const dataType = dataTypeMatch?.[1]?.trim() || 'additional';

    return { reasoning, action, query, dataType };
  } catch (error) {
    console.error('Error in performReasoning:', error);
    // Simplified fallback - let AI handle priorities even in error cases
    return {
      reasoning: `Iteration ${iteration}: Continuing research for ${companyName} - AI reasoning temporarily unavailable`,
      action: 'search',
      query: `${companyName} company information overview`,
      dataType: 'basic'
    };
  }
};

/**
 * Performs the search action using Tavily API.
 * @param query The search query.
 * @returns The search results and observation.
 */
const performAction = async (query: string): Promise<{results: SearchResult[], observation: string}> => {
  try {
    const tavilyResponse = await callTavilyAPI(query);
    const results = tavilyResponse.results || [];

    const observation = `Found ${results.length} search results. Key sources include: ${
      results.slice(0, 3).map(r => r.title).join(', ')
    }`;

    return { results, observation };
  } catch (error) {
    console.error('Error in performAction:', error);
    return {
      results: [],
      observation: `Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

// =================================================================
// BACKGROUND PROCESSING
// =================================================================

/**
 * Validates URLs to ensure they are accessible and not hallucinated.
 * @param urls Array of URLs to validate.
 * @returns Array of validated URLs.
 */
const validateUrls = async (urls: string[]): Promise<string[]> => {
  const validUrls: string[] = [];

  for (const url of urls) {
    try {
      // Basic URL format validation
      new URL(url);

      // Check if URL is accessible (with timeout)
      const response = await axios.head(url, {
        timeout: 5000,
        validateStatus: (status) => status < 500 // Accept redirects and client errors, but not server errors
      });

      if (response.status < 400) {
        validUrls.push(url);
      }
    } catch (error) {
      console.log(`URL validation failed for: ${url}`);
    }
  }

  return validUrls;
};

/**
 * Cross-references information across multiple search results to verify accuracy.
 * @param searchResults The search results to cross-reference.
 * @param companyName The company name for context.
 * @returns Verified information with confidence scores.
 */
const crossReferenceInformation = (searchResults: SearchResult[], companyName: string): {
  verifiedFacts: string[];
  suspiciousFacts: string[];
  urls: string[];
} => {
  const factCounts = new Map<string, number>();
  const urlSet = new Set<string>();

  // Extract URLs from search results
  searchResults.forEach(result => {
    urlSet.add(result.url);

    // Extract potential facts (simplified approach)
    const content = result.content.toLowerCase();
    const companyLower = companyName.toLowerCase();

    // Look for common fact patterns
    const patterns = [
      /founded in (\d{4})/g,
      /established in (\d{4})/g,
      /headquarters in ([^.]+)/g,
      /based in ([^.]+)/g,
      /(\d+) employees/g,
      /revenue of \$([^.]+)/g
    ];

    patterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const count = factCounts.get(match) || 0;
          factCounts.set(match, count + 1);
        });
      }
    });
  });

  // Separate verified facts (mentioned in multiple sources) from suspicious ones
  const verifiedFacts: string[] = [];
  const suspiciousFacts: string[] = [];

  factCounts.forEach((count, fact) => {
    // if (count >= 2) {
    //   verifiedFacts.push(fact);
    // } else {
    //   suspiciousFacts.push(fact);
    // }
    verifiedFacts.push(fact);
  });

  return {
    verifiedFacts,
    suspiciousFacts,
    urls: Array.from(urlSet)
  };
};

/**
 * Extracts and processes information from search results using AI with verification.
 * @param companyName The company name.
 * @param searchResults The search results to process.
 * @param currentInfo The current information gathered.
 * @param queryType The type of query (basic, news, financial, etc.).
 * @returns The extracted information.
 */
const extractInformationFromResults = async (
  companyName: string,
  searchResults: SearchResult[],
  currentInfo: Partial<CompanyInformation>,
  queryType: string
): Promise<ExtractionResult> => {
  // Cross-reference information for verification
  const { verifiedFacts, urls } = crossReferenceInformation(searchResults, companyName);

  // Validate URLs
  // const validUrls = await validateUrls(urls);
  const validUrls = urls;

  const extractionPrompt = `
You are a business research analyst extracting specific information about ${companyName} from search results.

Query Type: ${queryType}
Current Information: ${JSON.stringify(currentInfo, null, 2)}

Search Results:
${searchResults.map((result, index) =>
  `[Result ${index + 1}]\nTitle: ${result.title}\nURL: ${result.url}\nContent: ${result.content}\n`
).join('\n')}

Verified Facts (mentioned in multiple sources):
${verifiedFacts.join('\n')}

Valid URLs Available:
${validUrls.join('\n')}

CRITICAL VERIFICATION RULES:
1. Only use URLs from the "Valid URLs Available" list above
2. Prioritize information that appears in multiple search results
3. Cross-reference dates, numbers, and facts across sources
4. If information conflicts between sources, note the discrepancy
5. Do not generate or hallucinate any URLs not in the valid list

Extract and return ONLY new information that is not already in the current information. Focus on:
- Verifying and updating existing information with more accurate data
- Adding missing information relevant to the query type
- Including only URLs that are in the Valid URLs Available list
- Using exact dates and figures when available
- Do not add new key other than the ones in the JSON structure below

Return the information in this JSON structure (include only fields with new information):
{
  "company_profile": {
    "basic_info": {
      "company_name": "string",
      "logo_url": "string (if available)",
      "industry_category": "string (e.g., FMCG - snack & confectionery manufacturing)",
      "description": "string (2-3 sentences describing the business)",
      "location": "string (City, Region/State)",
      "employee_count": "string (e.g., 1,200 employees)",
      "established_year": "string (e.g., Est. 2000)",
      "website": "string (company website URL)"
    },
    "products_and_services": "string (few sentences explaining what the company offers as products/services)",
    "management_profile": {
      "name": "string",
      "position": "string (e.g., Founder & CEO, Operations Manager)",
      "background": "string (professional background description)"
    }[],
    "group_structure": {
      "parent_company": "string",
      "subsidiaries": {
        "name": "string",
        "description": "string"
      }[],
      "ownership_structure": "string (e.g., privately held, public equity details)"
    },
    "esg_initiatives": {
      "environmental": "string (environmental initiatives)"[],
      "social": "string (social responsibility programs)"[],
      "governance": "string (governance practices)"[]
    },
    "growth_metrics": {
      "cagr": {
        "percentage": "float (e.g., 0.39 for 39%)",
        "benchmark": "string (e.g., Industry Growth)",
        "data_points": {
          "year": "number",
          "value": "float (e.g., 2000000000.0, 4000000000.0)"
        }[]
      }
    },
    "industry_insights": {
      "title": "string",
      "description": "string (insights about the industry the company operates in, not about the company itself)"
    }[],
    "recent_news": {
      "title": "string",
      "date": "string (DD Month YYYY format)",
      "categories": ["string", "string"],
      "summary": "string (brief summary)",
      "source_url": "string (valid URL link to the news article)"
    }[],
    "recent_socmed_posts": {
      "title": "string",
      "date": "string (DD Month YYYY format)",
      "socmed_type": "string (enum: x, instagram, linkedin, facebook, youtube)",
      "summary": "string (brief summary)",
      "source_url": "string (valid URL link to the social media post)"
    }[]
  },
  "has_new_information": "boolean (true if there is new information, false otherwise)"
}

IMPORTANT: Only include URLs that are in the Valid URLs Available list above. Any URL not in that list will be considered hallucinated and invalid.
`;

  try {
    const response = await callGemini('You are a precise business research analyst. Extract only verified information from the provided search results. Never hallucinate URLs.', extractionPrompt);
    return response as ExtractionResult;
  } catch (error) {
    console.error('Error extracting information:', error);
    return { has_new_information: false };
  }
};

/**
 * Merges new information with existing information.
 * @param current The current information.
 * @param newInfo The new information to merge.
 * @returns The merged information.
 */
const mergeInformation = async (current: Partial<CompanyInformation>, newInfo: Partial<CompanyInformation>): Promise<Partial<CompanyInformation>> => {
  // Use gemini to merge the information
  const prompt = `
You are a precise business research analyst. Merge the new information with the existing information.

Existing Information: ${JSON.stringify(current, null, 2)}
New Information: ${JSON.stringify(newInfo, null, 2)}

Return the merged information in this JSON structure:
{
  "company_profile": {
    "basic_info": {
      "company_name": "string",
      "logo_url": "string (if available)",
      "industry_category": "string (e.g., FMCG - snack & confectionery manufacturing)",
      "description": "string (2-3 sentences describing the business)",
      "location": "string (City, Region/State)",
      "employee_count": "string (e.g., 1,200 employees)",
      "established_year": "string (e.g., Est. 2000)",
      "website": "string (company website URL)"
    },
    "products_and_services": "string (few sentences explaining what the company offers as products/services)",
    "management_profile": {
      "name": "string",
      "position": "string (e.g., Founder & CEO, Operations Manager)",
      "background": "string (professional background description)"
    }[],
    "group_structure": {
      "parent_company": "string",
      "subsidiaries": {
        "name": "string",
        "description": "string"
      }[],
      "ownership_structure": "string (e.g., privately held, public equity details)"
    },
    "esg_initiatives": {
      "environmental": "string (environmental initiatives)"[],
      "social": "string (social responsibility programs)"[],
      "governance": "string (governance practices)"[]
    },
    "growth_metrics": {
      "cagr": {
        "percentage": "float (e.g., 0.39 for 39%)",
        "benchmark": "string (e.g., Industry Growth)",
        "data_points": {
          "year": "number",
          "value": "float (e.g., 2000000000.0, 4000000000.0)"
        }[]
      }
    },
    "industry_insights": {
      "title": "string",
      "description": "string (insights about the industry the company operates in, not about the company itself)"
    }[],
    "recent_news": {
      "title": "string",
      "date": "string (DD Month YYYY format)",
      "categories": ["string", "string"],
      "summary": "string (brief summary)",
      "source_url": "string (valid URL link to the news article)"
    }[],
    "recent_socmed_posts": {
      "title": "string",
      "date": "string (DD Month YYYY format)",
      "socmed_type": "string (enum: x, instagram, linkedin, facebook, youtube)",
      "summary": "string (brief summary)",
      "source_url": "string (valid URL link to the social media post)"
    }[]
  }
}

IMPORTANT: Your final response must be the complete JSON object with no additional text afterward. The JSON should be properly formatted and valid.
`;

  try {
    const response = await callGemini('You are a precise business research analyst. Merge the new information with the existing information.', prompt);
    return response as Partial<CompanyInformation>;
  } catch (error) {
    console.error('Error merging information:', error);
    return current;
  }
};

const checkSufficientInformation = async (currentInfo: Partial<CompanyInformation>): Promise<boolean> => {
  const prompt = `
You are a precise business research analyst. Evaluate the completeness of given company profile information.

Return true if the company profile is complete and contains all the necessary information. Otherwise, return false.

You must return in valid JSON format.
{
  "is_complete": boolean
}
`;

  try {
    const response = await callGemini(`Please evaluate the completeness of the following company profile information: ${JSON.stringify(currentInfo, null, 2)}`, prompt);
    const result = response as { is_complete?: boolean };
    return result.is_complete ?? false;
  } catch (error) {
    console.error('Error checking information completeness:', error);
    return false;
  }
};

/**
 * Processes company profiling using ReAct paradigm with multiple search iterations.
 */
const processCompanyProfilingInBackground = async (jobId: string, companyName: string) => {
  try {
    // Update job status to processing
    const job = jobs.get(jobId);
    if (!job) return;

    job.status = 'processing';
    job.updatedAt = new Date();
    jobs.set(jobId, job);

    console.log(`\n--- Starting ReAct-based profiling for: ${companyName} (Job ID: ${jobId}) ---`);

    let currentInfo: Partial<CompanyInformation> = {};
    const iterations: ReActIteration[] = [];
    const maxIterations = 10;
    const previousQueries: string[] = [];
    const failedDataTypes: Set<string> = new Set();

    // ReAct Loop: Reason → Act → Observe → Plan
    for (let i = 1; i <= maxIterations; i++) {
      console.log(`\n--- ReAct Iteration ${i} ---`);

      // REASON: Determine what information is still needed
      const { reasoning, action, query, dataType } = await performReasoning(companyName, currentInfo, i, previousQueries, failedDataTypes);
      console.log(`Reasoning: ${reasoning}`);
      console.log(`Action: ${action}`);
      console.log(`Query: ${query}`);
      console.log(`Data Type: ${dataType}`);

      // Track this query to avoid duplicates
      previousQueries.push(query);

      // ACT: Perform the search
      const { results, observation } = await performAction(query);
      console.log(`Observation: ${observation}`);

      // OBSERVE: Extract information from search results
      const extractedInfo = await extractInformationFromResults(companyName, results, currentInfo, dataType);
      // console.log('Extracted Info:', JSON.stringify(extractedInfo, null, 2));

      // Check if we got meaningful data for this data type
      const hasNewData = extractedInfo.has_new_information;
      console.log(`Has New Data: ${hasNewData}`);
      if (!hasNewData) {
        console.log(`No data found for ${dataType}, marking as failed`);
        failedDataTypes.add(dataType);
      }

      // Merge new information
      currentInfo = await mergeInformation(currentInfo, extractedInfo);
      console.log('Current Info:', JSON.stringify(currentInfo, null, 2));

      // Store iteration details
      iterations.push({
        reasoning,
        action,
        query,
        observation,
        information_gathered: extractedInfo,
        dataType
      });

      // Smart termination logic - let AI decide when we have enough information
      const hasBasicInfo = currentInfo.basic_info?.company_name && currentInfo.basic_info?.industry_category;

      // Early termination if we have basic info and some meaningful data after a few iterations
      // if (i >= 4 && hasBasicInfo) {
      //   const hasAnyMeaningfulData =
      //     (currentInfo.recent_news && currentInfo.recent_news.length > 0) ||
      //     (currentInfo.recent_socmed_posts && currentInfo.recent_socmed_posts.length > 0) ||
      //     (currentInfo.management_profile && currentInfo.management_profile.length > 0) ||
      //     (currentInfo.products_and_services && currentInfo.products_and_services.length > 0);

      //   if (hasAnyMeaningfulData) {
      //     console.log(`Basic information and some meaningful data gathered after ${i} iterations.`);
      //     break;
      //   }
      // }

      // Use AI-based completeness check for mid-to-late iterations
      if (i >= 5) {
        const hasSufficientInfo = await checkSufficientInformation(currentInfo);
        if (hasSufficientInfo) {
          console.log(`AI determined sufficient information gathered after ${i} iterations.`);
          break;
        }
      }

      // Hard stop if too many data types have failed (likely company has limited online presence)
      if (failedDataTypes.size >= 4) {
        console.log(`Multiple data types failed (${failedDataTypes.size}), stopping search after ${i} iterations.`);
        break;
      }
    }

    // Final processing: Create complete profile using system prompt
    console.log("\n--- Final Profile Generation ---");
    const systemInstruction = readPromptFile('system_prompt.md');
    const finalPrompt = `
**Company to Research**: ${companyName}

**Information Gathered from ReAct Research**:
${JSON.stringify(currentInfo, null, 2)}

**Research Iterations**:
${iterations.map((iter, index) =>
  `Iteration ${index + 1}:\n- Reasoning: ${iter.reasoning}\n- Query: ${iter.query}\n- Observation: ${iter.observation}`
).join('\n\n')}

Using the above research data, create a comprehensive company profile in the exact JSON format specified in the system instructions. Ensure all URLs are valid and came from the research results above.
`;

    const finalProfile = await callGemini(systemInstruction, finalPrompt);

    // Update job with successful result
    job.status = 'completed';
    job.result = finalProfile;
    job.updatedAt = new Date();
    jobs.set(jobId, job);

    console.log(`--- ReAct profiling for ${companyName} completed successfully (Job ID: ${jobId}). ---`);
  } catch (error) {
    // Update job with error
    const job = jobs.get(jobId);
    if (job) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'An unknown error occurred';
      job.updatedAt = new Date();
      jobs.set(jobId, job);
    }

    console.error(`--- ReAct profiling for ${companyName} failed (Job ID: ${jobId}). ---`);
    console.error('Error:', error);
  }
};

// =================================================================
// ROUTE HANDLERS
// =================================================================

/**
 * Handles user login requests.
 */
const handleLogin = (req: Request, res: Response) => {
  const { email, password } = req.body;

  if (email === CONFIG.login.email && password === CONFIG.login.password) {
    res.json({
      success: true,
      message: 'Login successful',
      token: 'dummy-jwt-token', // In production, use a proper JWT library
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid email or password',
    });
  }
};

/**
 * Handles company profiling requests by starting a background job.
 */
const handleCompanyProfiling = async (req: Request, res: Response) => {
  const { companyName } = req.body;
  if (!companyName) {
    return res.status(400).json({ success: false, message: 'Company name is required' });
  }

  // Create a new job
  const jobId = uuidv4();
  const job: JobStatus = {
    id: jobId,
    status: 'pending',
    companyName: companyName.trim(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  jobs.set(jobId, job);

  // Start background processing (don't await)
  processCompanyProfilingInBackground(jobId, companyName.trim()).catch(error => {
    console.error('Background processing error:', error);
  });

  // Return immediately with job ID
  res.json({
    success: true,
    jobId: jobId,
    message: 'Company profiling started. Use the job ID to check status.',
  });
};

/**
 * Handles job status requests.
 */
const handleJobStatus = (req: Request, res: Response) => {
  const { jobId } = req.params;

  if (!jobId) {
    return res.status(400).json({ success: false, message: 'Job ID is required' });
  }

  const job = jobs.get(jobId);

  if (!job) {
    return res.status(404).json({ success: false, message: 'Job not found' });
  }

  // Return job status without internal details
  const response: {
    success: boolean;
    jobId: string;
    status: string;
    companyName: string;
    createdAt: Date;
    updatedAt: Date;
    data?: object;
    error?: string;
  } = {
    success: true,
    jobId: job.id,
    status: job.status,
    companyName: job.companyName,
    createdAt: job.createdAt,
    updatedAt: job.updatedAt,
  };

  if (job.status === 'completed' && job.result) {
    response.data = job.result;
  }

  if (job.status === 'failed' && job.error) {
    response.error = job.error;
  }

  res.json(response);
};

/**
 * Handles health check requests.
 */
const handleHealthCheck = (_req: Request, res: Response) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
};

// =================================================================
// EXPRESS APP SETUP
// =================================================================

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// API Routes
app.post('/login', handleLogin);
app.post('/company_profiling', handleCompanyProfiling);
app.get('/job/:jobId', handleJobStatus);
app.get('/health', handleHealthCheck);

// =================================================================
// SERVER START
// =================================================================

app.listen(CONFIG.port, () => {
  console.log(`Server running on port ${CONFIG.port}`);
});
