## JSON Output Format

```json
{
  "company_profile": {
    "basic_info": {
      "company_name": "string",
      "logo_url": "string (if available)",
      "industry_category": "string (e.g., FMCG - snack & confectionery manufacturing)",
      "description": "string (2-3 sentences describing the business)",
      "location": "string (City, Region/State)",
      "employee_count": "string (e.g., 1,200 employees)",
      "established_year": "string (e.g., Est. 2000)",
      "website": "string (company website URL)"
    },
    "products_and_services": "string (few sentences explaining what the company offers as products/services)",
    "management_profile": {
      "name": "string",
      "position": "string (e.g., Founder & CEO, Operations Manager)",
      "background": "string (professional background description)"
    }[],
    "group_structure": {
      "parent_company": "string",
      "subsidiaries": {
        "name": "string",
        "description": "string"
      }[],
      "ownership_structure": "string (e.g., privately held, public equity details)"
    },
    "esg_initiatives": {
      "environmental": "string (environmental initiatives)"[],
      "social": "string (social responsibility programs)"[],
      "governance": "string (governance practices)"[]
    },
    "growth_metrics": {
      "cagr": {
        "percentage": "float (e.g., 0.39 for 39%)",
        "benchmark": "string (e.g., Industry Growth)",
        "data_points": {
          "year": "number",
          "value": "float (e.g., 2000000000.0, 4000000000.0)"
        }[]
      }
    },
    "industry_insights": {
      "title": "string",
      "description": "string (insights about the industry the company operates in, not about the company itself)"
    }[],
    "recent_news": {
      "title": "string",
      "date": "string (DD Month YYYY format)",
      "categories": ["string", "string"],
      "summary": "string (brief summary)",
      "source_url": "string (valid URL link to the news article)"
    }[],
    "recent_socmed_posts": {
      "title": "string",
      "date": "string (DD Month YYYY format)",
      "socmed_type": "string (enum: x, instagram, linkedin, facebook, youtube)",
      "summary": "string (brief summary)",
      "source_url": "string (valid URL link to the social media post)"
    }[]
  }
}
```