import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Users2 } from "lucide-react";

interface ManagementProfile {
  name: string;
  position: string;
  background: string;
}

interface ManagementSectionProps {
  data: ManagementProfile[];
}

const ManagementSection = ({ data }: ManagementSectionProps) => {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Card className="card-elegant">
      <CardHeader>
        <CardTitle className="section-header">
          <Users2 className="w-5 h-5 text-primary" />
          Management profile
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {data.map((member, index) => (
          <div key={index} className="flex items-start gap-4 p-4 rounded-lg border border-border bg-muted/30">
            <Avatar className="w-12 h-12">
              <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                {getInitials(member.name)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h3 className="font-semibold text-foreground">{member.name}</h3>
              <p className="text-sm text-primary font-medium mb-2">{member.position}</p>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {member.background}
              </p>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default ManagementSection;