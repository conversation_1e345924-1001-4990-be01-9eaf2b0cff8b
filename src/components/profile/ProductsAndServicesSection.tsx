import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Package } from "lucide-react";

interface ProductsAndServicesSectionProps {
  data: string;
}

const ProductsAndServicesSection = ({ data }: ProductsAndServicesSectionProps) => {
  return (
    <Card className="card-elegant">
      <CardHeader>
        <CardTitle className="section-header">
          <Package className="w-5 h-5 text-primary" />
          Products & Services
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground leading-relaxed">
          {data}
        </p>
      </CardContent>
    </Card>
  );
};

export default ProductsAndServicesSection;
