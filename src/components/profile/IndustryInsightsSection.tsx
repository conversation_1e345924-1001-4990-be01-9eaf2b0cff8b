import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Lightbulb } from "lucide-react";

interface IndustryInsight {
  title: string;
  description: string;
}

interface IndustryInsightsSectionProps {
  data: IndustryInsight[];
}

const IndustryInsightsSection = ({ data }: IndustryInsightsSectionProps) => {
  return (
    <Card className="card-elegant">
      <CardHeader>
        <CardTitle className="section-header">
          <Lightbulb className="w-5 h-5 text-primary" />
          Industry Insights
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {data.map((insight, index) => (
          <div key={index} className="p-4 rounded-lg border border-border bg-muted/30 space-y-2">
            <h3 className="font-semibold text-foreground flex items-start gap-2">
              <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0 mt-2" />
              {insight.title}
            </h3>
            <p className="text-sm text-muted-foreground leading-relaxed ml-4">
              {insight.description}
            </p>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default IndustryInsightsSection;