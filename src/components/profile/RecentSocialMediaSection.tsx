import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Share2, Calendar, ExternalLink } from "lucide-react";

interface RecentSocialMediaPost {
  title: string;
  date: string;
  socmed_type: "x" | "instagram" | "linkedin" | "facebook" | "youtube";
  summary: string;
  source_url: string;
}

interface RecentSocialMediaSectionProps {
  data: RecentSocialMediaPost[];
}

const RecentSocialMediaSection = ({ data }: RecentSocialMediaSectionProps) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const getSocialMediaColor = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'x':
        return 'bg-slate-100 text-slate-800 border-slate-200';
      case 'instagram':
        return 'bg-pink-100 text-pink-800 border-pink-200';
      case 'linkedin':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'facebook':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'youtube':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-secondary text-secondary-foreground border-secondary';
    }
  };

  const getSocialMediaIcon = (platform: string) => {
    // For now, we'll use a generic share icon
    // In a real app, you might want to use specific social media icons
    return <Share2 className="w-3 h-3" />;
  };

  return (
    <Card className="card-elegant">
      <CardHeader>
        <CardTitle className="section-header">
          <Share2 className="w-5 h-5 text-primary" />
          Recent Social Media Posts
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {data.map((post, index) => (
          <div key={index} className="space-y-3 pb-4 border-b border-border last:border-b-0 last:pb-0">
            <div>
              <a 
                href={post.source_url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="group block"
              >
                <h3 className="font-semibold text-foreground leading-tight mb-2 group-hover:text-primary transition-colors flex items-start gap-2">
                  {post.title}
                  <ExternalLink className="w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors flex-shrink-0 mt-0.5" />
                </h3>
              </a>
              <div className="flex items-center gap-3 mb-3">
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Calendar className="w-3 h-3" />
                  {formatDate(post.date)}
                </div>
                <Badge 
                  variant="outline" 
                  className={`text-xs flex items-center gap-1 ${getSocialMediaColor(post.socmed_type)}`}
                >
                  {getSocialMediaIcon(post.socmed_type)}
                  {post.socmed_type.charAt(0).toUpperCase() + post.socmed_type.slice(1)}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {post.summary}
              </p>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default RecentSocialMediaSection;
