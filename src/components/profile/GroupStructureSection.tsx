import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Network } from "lucide-react";

interface Subsidiary {
  name: string;
  description?: string;
}

interface GroupStructure {
  parent_company: string;
  subsidiaries: Subsidiary[];
  ownership_structure: string;
}

interface GroupStructureSectionProps {
  data: GroupStructure;
}

const GroupStructureSection = ({ data }: GroupStructureSectionProps) => {
  return (
    <Card className="card-elegant">
      <CardHeader>
        <CardTitle className="section-header">
          <Network className="w-5 h-5 text-primary" />
          Group Structure & Subsidiaries
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="p-4 rounded-lg border border-primary/20 bg-primary/5">
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold text-foreground mb-2">
                {data.parent_company === "Independent" ? "Independent Company" : data.parent_company}
              </h3>
              {data.parent_company === "Independent" && (
                <p className="text-sm text-muted-foreground">
                  No external public equity; privately held by founder and co-founders
                </p>
              )}
            </div>
            
            {data.subsidiaries.length > 0 && (
              <div>
                <h4 className="font-medium text-foreground mb-3">Subsidiaries:</h4>
                <div className="space-y-3">
                  {data.subsidiaries.map((subsidiary, index) => (
                    <div key={index} className="p-3 rounded-md border border-border bg-card">
                      <div className="flex items-start gap-2">
                        <Badge variant="outline" className="shrink-0">
                          {subsidiary.name}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2 leading-relaxed">
                        {subsidiary.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div>
          <h4 className="font-medium text-foreground mb-2">Ownership Structure</h4>
          <p className="text-sm text-muted-foreground leading-relaxed">
            {data.ownership_structure}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default GroupStructureSection;