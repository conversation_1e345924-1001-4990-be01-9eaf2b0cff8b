import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Building2, MapPin, Users, Calendar, Globe } from "lucide-react";

interface BasicInfo {
  company_name: string;
  logo_url: string;
  industry_category: string;
  description: string;
  location: string;
  employee_count: string;
  established_year: string;
  website: string;
}

interface BasicInfoSectionProps {
  data: BasicInfo;
}

const BasicInfoSection = ({ data }: BasicInfoSectionProps) => {
  return (
    <Card className="card-elegant">
      <CardContent className="p-6">
        <div className="flex items-start gap-6">
          <div className="flex-shrink-0">
            <div className="w-20 h-20 bg-primary/10 rounded-lg flex items-center justify-center p-3">
              <img 
                src={data.logo_url} 
                alt={data.company_name}
                className="w-full h-full object-contain"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.nextElementSibling?.classList.remove('hidden');
                }}
              />
              <Building2 className="w-10 h-10 text-primary hidden" />
            </div>
          </div>
          
          <div className="flex-1 space-y-4">
            <div>
              <h1 className="text-2xl font-bold text-foreground mb-2">{data.company_name}</h1>
              <Badge variant="secondary" className="mb-3">
                {data.industry_category}
              </Badge>
              <p className="text-muted-foreground leading-relaxed">
                {data.description}
              </p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="w-4 h-4 text-primary" />
                <span className="text-muted-foreground">{data.location}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Users className="w-4 h-4 text-primary" />
                <span className="text-muted-foreground">{data.employee_count}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="w-4 h-4 text-primary" />
                <span className="text-muted-foreground">{data.established_year}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Globe className="w-4 h-4 text-primary" />
                <a 
                  href={data.website} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-primary hover:underline"
                >
                  {data.website.replace('https://', '')}
                </a>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BasicInfoSection;