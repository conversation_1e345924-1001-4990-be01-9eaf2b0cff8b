import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Leaf, Users2, Shield } from "lucide-react";

interface ESGInitiatives {
  environmental: string[];
  social: string[];
  governance: string[];
}

interface ESGSectionProps {
  data: ESGInitiatives;
}

const ESGSection = ({ data }: ESGSectionProps) => {
  const renderInitiativesList = (items: string[], icon: React.ReactNode, title: string, color: string) => (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        {icon}
        <h3 className="font-semibold text-foreground">{title}</h3>
      </div>
      {items.length > 0 && items[0] !== "Information not found" ? (
        <ul className="space-y-2">
          {items.map((item, index) => (
            <li key={index} className="flex items-start gap-2 text-sm text-muted-foreground">
              <div className={`w-1.5 h-1.5 ${color} rounded-full flex-shrink-0 mt-2`} />
              <span className="leading-relaxed">{item}</span>
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-sm text-muted-foreground italic">Information not found</p>
      )}
    </div>
  );

  return (
    <Card className="card-elegant">
      <CardHeader>
        <CardTitle className="section-header">
          <Leaf className="w-5 h-5 text-primary" />
          ESG and CSR Initiatives
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {renderInitiativesList(
          data.environmental,
          <Leaf className="w-5 h-5 text-success" />,
          "Environmental",
          "bg-success"
        )}
        
        {renderInitiativesList(
          data.social,
          <Users2 className="w-5 h-5 text-info" />,
          "Social",
          "bg-info"
        )}
        
        {renderInitiativesList(
          data.governance,
          <Shield className="w-5 h-5 text-warning" />,
          "Governance",
          "bg-warning"
        )}
      </CardContent>
    </Card>
  );
};

export default ESGSection;