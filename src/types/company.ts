export interface Subsidiary {
  name: string;
  description?: string;
}

export interface CompanyProfile {
  company_profile: {
    basic_info: {
      company_name: string;
      logo_url: string;
      industry_category: string;
      description: string;
      location: string;
      employee_count: string;
      established_year: string;
      website: string;
    };
    products_and_services: string;
    management_profile: Array<{
      name: string;
      position: string;
      background: string;
    }>;
    group_structure: {
      parent_company: string;
      subsidiaries: Subsidiary[];
      ownership_structure: string;
    };
    esg_initiatives: {
      environmental: string[];
      social: string[];
      governance: string[];
    };
    growth_metrics: {
      cagr: {
        percentage: number | null;
        benchmark: string;
        data_points: Array<{
          year: string;
          value: number;
        }>;
      };
    };
    industry_insights: Array<{
      title: string;
      description: string;
    }>;
    recent_news: Array<{
      title: string;
      date: string;
      categories: string[];
      summary: string;
      source_url: string;
    }>;
    recent_socmed_posts: Array<{
      title: string;
      date: string;
      socmed_type: "x" | "instagram" | "linkedin" | "facebook" | "youtube";
      summary: string;
      source_url: string;
    }>;
  };
}