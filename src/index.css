@tailwind base;
@tailwind components;
@tailwind utilities;

/* Rakamin-inspired professional design system */

@layer base {
  :root {
    --background: 250 100% 99%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 177 100% 35%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 177 100% 30%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 98%;
    --muted-foreground: 215 16% 47%;

    --accent: 177 50% 95%;
    --accent-foreground: 177 100% 25%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 177 100% 35%;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;

    --info: 217 91% 60%;
    --info-foreground: 0 0% 100%;

    --radius: 0.75rem;

    /* Professional gradients */
    --gradient-primary: linear-gradient(135deg, hsl(177 100% 35%), hsl(177 100% 45%));
    --gradient-subtle: linear-gradient(180deg, hsl(250 100% 99%), hsl(210 40% 98%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(210 40% 99%));

    /* Professional shadows */
    --shadow-elegant: 0 4px 20px -4px hsl(177 100% 35% / 0.1);
    --shadow-card: 0 2px 10px -2px hsl(222 47% 11% / 0.08);
    --shadow-hover: 0 8px 30px -8px hsl(177 100% 35% / 0.2);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    background: var(--gradient-subtle);
  }
}

@layer components {
  .card-elegant {
    @apply bg-card border border-border rounded-lg shadow-sm;
    background: var(--gradient-card);
    box-shadow: var(--shadow-card);
    transition: var(--transition-smooth);
  }

  .card-elegant:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground font-medium rounded-lg transition-all duration-300;
    background: var(--gradient-primary);
    box-shadow: var(--shadow-elegant);
  }

  .btn-primary:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-1px);
  }

  .section-header {
    @apply text-lg font-semibold text-foreground mb-4 flex items-center gap-2;
  }

  .metric-card {
    @apply p-4 rounded-lg border border-border bg-card;
    background: var(--gradient-card);
    transition: var(--transition-smooth);
  }

  .metric-card:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-card);
  }
}