# Multi-stage Dockerfile for Rakamin Profile Viz
# Stage 1: Build the frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig*.json ./
COPY vite.config.ts ./
COPY tailwind.config.ts ./
COPY postcss.config.js ./
COPY components.json ./
COPY eslint.config.js ./

# Install dependencies
RUN npm ci

# Copy source code
COPY src/ ./src/
COPY public/ ./public/
COPY index.html ./

# Build the frontend
RUN NODE_ENV=production npm run build

# Stage 2: Setup backend
FROM node:18-alpine AS backend-builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig*.json ./

# Install dependencies (including dev dependencies for TypeScript compilation)
RUN npm ci

# Copy server source code
COPY server/ ./server/

# Create a simple tsconfig for compilation
RUN echo '{"compilerOptions":{"target":"ES2020","module":"NodeNext","moduleResolution":"NodeNext","outDir":"./dist","rootDir":"./server","esModuleInterop":true,"allowSyntheticDefaultImports":true,"skipLibCheck":true,"strict":false}}' > tsconfig.server.json

# Compile TypeScript to JavaScript
RUN npx tsc --project tsconfig.server.json

# Stage 3: Production runtime with nginx
FROM nginx:alpine

# Install Node.js in the nginx container
RUN apk add --no-cache nodejs npm

# Create app directory
WORKDIR /app

# Copy package.json and install only production dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy compiled backend from builder stage
COPY --from=backend-builder /app/dist ./dist

# Copy backend prompts
COPY server/prompts ./prompts

# Copy built frontend from builder stage
COPY --from=frontend-builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy startup script
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

# Expose port 80
EXPOSE 80

# Use custom entrypoint to start both nginx and node
ENTRYPOINT ["/docker-entrypoint.sh"]
